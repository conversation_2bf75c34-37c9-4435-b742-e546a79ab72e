import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MessageSquare, Users, Target, Award } from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";
interface AboutUsPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}
const AboutUsPage = ({ onBack, onAuth, onNavigate }: AboutUsPageProps) => {
  return (
    <div className="min-h-screen bg-white">
      <Navigation onNavigate={onNavigate} onAuth={onAuth} currentPage="about" />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-white">
            About Wispr SMS
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            We're dedicated to helping businesses connect with their customers
            through powerful SMS marketing solutions.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Our Mission
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              To empower businesses with reliable, efficient, and user-friendly
              SMS marketing tools that drive growth and customer engagement.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <MessageSquare className="h-12 w-12 text-[#368f98] mx-auto mb-4" />
                <CardTitle className="text-lg">Innovation</CardTitle>
                <CardDescription>
                  Continuously improving our platform with cutting-edge
                  technology.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="h-12 w-12 text-[#368f98] mx-auto mb-4" />
                <CardTitle className="text-lg">Customer Focus</CardTitle>
                <CardDescription>
                  Putting our customers' success at the center of everything we
                  do.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Target className="h-12 w-12 text-[#368f98] mx-auto mb-4" />
                <CardTitle className="text-lg">Reliability</CardTitle>
                <CardDescription>
                  Delivering consistent, high-quality service you can depend on.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <Award className="h-12 w-12 text-[#368f98] mx-auto mb-4" />
                <CardTitle className="text-lg">Excellence</CardTitle>
                <CardDescription>
                  Striving for excellence in every aspect of our platform and
                  service.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};
export default AboutUsPage;
