
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON><PERSON>hart, Pie, Cell } from "recharts";
import { TrendingUp, TrendingDown, MessageSquare, Users, CheckCircle, XCircle, Clock, AlertTriangle } from "lucide-react";

const CampaignAnalytics = () => {
  const overallStats = {
    totalSent: 45632,
    delivered: 44891,
    failed: 741,
    deliveryRate: 98.4,
    clickThrough: 12.3,
    optOuts: 23,
  };

  const weeklyData = [
    { day: "Mon", sent: 1250, delivered: 1230, clicked: 145 },
    { day: "Tue", sent: 1100, delivered: 1085, clicked: 132 },
    { day: "Wed", sent: 1350, delivered: 1335, clicked: 165 },
    { day: "Thu", sent: 1180, delivered: 1165, clicked: 140 },
    { day: "Fri", sent: 1420, delivered: 1398, clicked: 172 },
    { day: "Sat", sent: 980, delivered: 970, clicked: 110 },
    { day: "Sun", sent: 850, delivered: 840, clicked: 95 },
  ];

  const campaignPerformance = [
    { name: "Holiday Sale", sent: 5000, delivered: 4950, clicked: 612, deliveryRate: 99.0, ctr: 12.4 },
    { name: "Weekly Newsletter", sent: 12000, delivered: 11820, clicked: 1418, deliveryRate: 98.5, ctr: 12.0 },
    { name: "Flash Sale", sent: 3500, delivered: 3465, clicked: 485, deliveryRate: 99.0, ctr: 14.0 },
    { name: "Product Update", sent: 8500, delivered: 8330, clicked: 916, deliveryRate: 98.0, ctr: 11.0 },
    { name: "Event Reminder", sent: 2800, delivered: 2772, clicked: 360, deliveryRate: 99.0, ctr: 13.0 },
  ];

  const deviceData = [
    { name: "Mobile", value: 75, color: "#3b82f6" },
    { name: "Desktop", value: 20, color: "#10b981" },
    { name: "Tablet", value: 5, color: "#f59e0b" },
  ];

  const timeData = [
    { hour: "9 AM", opens: 450 },
    { hour: "10 AM", opens: 520 },
    { hour: "11 AM", opens: 680 },
    { hour: "12 PM", opens: 720 },
    { hour: "1 PM", opens: 590 },
    { hour: "2 PM", opens: 640 },
    { hour: "3 PM", opens: 700 },
    { hour: "4 PM", opens: 580 },
    { hour: "5 PM", opens: 450 },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Campaign Analytics</h2>
        <p className="text-muted-foreground">Track and analyze your SMS campaign performance</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Sent</p>
                <p className="text-2xl font-bold">{overallStats.totalSent.toLocaleString()}</p>
                <div className="flex items-center text-sm text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +12% vs last month
                </div>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Delivery Rate</p>
                <p className="text-2xl font-bold">{overallStats.deliveryRate}%</p>
                <div className="flex items-center text-sm text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +0.3% vs last month
                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Click Rate</p>
                <p className="text-2xl font-bold">{overallStats.clickThrough}%</p>
                <div className="flex items-center text-sm text-red-600">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  -0.8% vs last month
                </div>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Opt-outs</p>
                <p className="text-2xl font-bold">{overallStats.optOuts}</p>
                <div className="flex items-center text-sm text-green-600">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  -15% vs last month
                </div>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Performance</CardTitle>
            <CardDescription>Messages sent and delivered over the past week</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sent" fill="#3b82f6" name="Sent" />
                <Bar dataKey="delivered" fill="#10b981" name="Delivered" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Engagement by Time */}
        <Card>
          <CardHeader>
            <CardTitle>Best Engagement Times</CardTitle>
            <CardDescription>When your audience is most active</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={timeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="opens" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Device Breakdown and Campaign Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Device Breakdown</CardTitle>
            <CardDescription>How recipients view your messages</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Campaigns */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Campaign Performance</CardTitle>
            <CardDescription>Your recent campaigns and their metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {campaignPerformance.map((campaign, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{campaign.name}</h4>
                    <div className="flex gap-2">
                      <Badge variant="secondary">{campaign.deliveryRate}% delivered</Badge>
                      <Badge variant="outline">{campaign.ctr}% CTR</Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{campaign.sent.toLocaleString()} sent</span>
                    <span>{campaign.clicked.toLocaleString()} clicked</span>
                  </div>
                  <Progress value={campaign.deliveryRate} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery Status Breakdown</CardTitle>
          <CardDescription>Detailed breakdown of message delivery status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{overallStats.delivered.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Delivered</div>
              <div className="text-xs text-green-600">{overallStats.deliveryRate}% of total</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-yellow-600">127</div>
              <div className="text-sm text-muted-foreground">Pending</div>
              <div className="text-xs text-yellow-600">0.3% of total</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-600">{overallStats.failed}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
              <div className="text-xs text-red-600">1.6% of total</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-orange-600">{overallStats.optOuts}</div>
              <div className="text-sm text-muted-foreground">Opt-outs</div>
              <div className="text-xs text-orange-600">0.1% of total</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CampaignAnalytics;
