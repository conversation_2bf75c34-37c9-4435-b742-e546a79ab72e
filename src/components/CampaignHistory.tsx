
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, MoreHorizontal, Eye, Copy, Trash2, Calendar, MessageSquare, Users, BarChart3 } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

const CampaignHistory = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);

  const campaigns = [
    {
      id: 1,
      name: "Holiday Sale Alert",
      status: "completed",
      sentDate: "2024-01-15 14:30",
      recipients: 2500,
      delivered: 2475,
      failed: 25,
      clicked: 312,
      deliveryRate: 99.0,
      clickRate: 12.6,
      message: "🎉 Holiday Sale is here! Get up to 50% off on all items. Use code HOLIDAY50. Shop now: link.com/sale",
      group: "customers"
    },
    {
      id: 2,
      name: "Weekly Newsletter",
      status: "completed",
      sentDate: "2024-01-14 09:00",
      recipients: 5000,
      delivered: 4925,
      failed: 75,
      clicked: 590,
      deliveryRate: 98.5,
      clickRate: 12.0,
      message: "Weekly updates: New products, company news, and special offers just for you!",
      group: "all"
    },
    {
      id: 3,
      name: "Flash Sale Announcement",
      status: "completed",
      sentDate: "2024-01-13 16:15",
      recipients: 1200,
      delivered: 1188,
      failed: 12,
      clicked: 166,
      deliveryRate: 99.0,
      clickRate: 14.0,
      message: "⚡ FLASH SALE: 4 hours only! 30% off everything. Code: FLASH30",
      group: "vip"
    },
    {
      id: 4,
      name: "Product Update Notification",
      status: "scheduled",
      sentDate: "2024-01-16 10:00",
      recipients: 3200,
      delivered: 0,
      failed: 0,
      clicked: 0,
      deliveryRate: 0,
      clickRate: 0,
      message: "Exciting product updates are here! Check out the new features we've added.",
      group: "customers"
    },
    {
      id: 5,
      name: "Event Reminder",
      status: "draft",
      sentDate: null,
      recipients: 800,
      delivered: 0,
      failed: 0,
      clicked: 0,
      deliveryRate: 0,
      clickRate: 0,
      message: "Don't forget about our upcoming webinar tomorrow at 2 PM EST.",
      group: "prospects"
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "scheduled":
        return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>;
      case "draft":
        return <Badge variant="secondary">Draft</Badge>;
      case "failed":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || campaign.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalStats = {
    totalCampaigns: campaigns.length,
    totalSent: campaigns.reduce((sum, c) => sum + (c.delivered + c.failed), 0),
    avgDeliveryRate: campaigns.filter(c => c.status === "completed").reduce((sum, c) => sum + c.deliveryRate, 0) / campaigns.filter(c => c.status === "completed").length,
    avgClickRate: campaigns.filter(c => c.status === "completed").reduce((sum, c) => sum + c.clickRate, 0) / campaigns.filter(c => c.status === "completed").length,
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">Campaign History</h2>
          <p className="text-muted-foreground">View and manage your SMS campaigns</p>
        </div>
        <Button>
          <MessageSquare className="h-4 w-4 mr-2" />
          New Campaign
        </Button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Campaigns</p>
                <p className="text-2xl font-bold">{totalStats.totalCampaigns}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Messages Sent</p>
                <p className="text-2xl font-bold">{totalStats.totalSent.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Delivery Rate</p>
                <p className="text-2xl font-bold">{totalStats.avgDeliveryRate.toFixed(1)}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Click Rate</p>
                <p className="text-2xl font-bold">{totalStats.avgClickRate.toFixed(1)}%</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Sent Date</TableHead>
                <TableHead>Recipients</TableHead>
                <TableHead>Delivery Rate</TableHead>
                <TableHead>Click Rate</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCampaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell className="font-medium">{campaign.name}</TableCell>
                  <TableCell>{getStatusBadge(campaign.status)}</TableCell>
                  <TableCell>
                    {campaign.sentDate ? new Date(campaign.sentDate).toLocaleDateString() : "-"}
                  </TableCell>
                  <TableCell>{campaign.recipients.toLocaleString()}</TableCell>
                  <TableCell>
                    {campaign.status === "completed" ? (
                      <div className="flex items-center">
                        <div className="text-sm font-medium">{campaign.deliveryRate}%</div>
                        <div className="text-xs text-muted-foreground ml-1">
                          ({campaign.delivered}/{campaign.recipients})
                        </div>
                      </div>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    {campaign.status === "completed" ? `${campaign.clickRate}%` : "-"}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <Dialog>
                          <DialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => {
                              e.preventDefault();
                              setSelectedCampaign(campaign);
                            }}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                          </DialogTrigger>
                        </Dialog>
                        <DropdownMenuItem>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Campaign Details Dialog */}
      {selectedCampaign && (
        <Dialog open={!!selectedCampaign} onOpenChange={() => setSelectedCampaign(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Campaign Details: {selectedCampaign.name}</DialogTitle>
              <DialogDescription>
                Detailed information and metrics for this campaign
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Campaign Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="mt-1">{getStatusBadge(selectedCampaign.status)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Target Group</Label>
                  <p className="mt-1 capitalize">{selectedCampaign.group}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Recipients</Label>
                  <p className="mt-1">{selectedCampaign.recipients.toLocaleString()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Sent Date</Label>
                  <p className="mt-1">
                    {selectedCampaign.sentDate ? new Date(selectedCampaign.sentDate).toLocaleString() : "Not sent"}
                  </p>
                </div>
              </div>

              {/* Performance Metrics */}
              {selectedCampaign.status === "completed" && (
                <div>
                  <Label className="text-sm font-medium">Performance Metrics</Label>
                  <div className="grid grid-cols-3 gap-4 mt-2">
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-green-600">{selectedCampaign.delivered}</div>
                      <div className="text-sm text-green-600">Delivered</div>
                    </div>
                    <div className="bg-red-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-red-600">{selectedCampaign.failed}</div>
                      <div className="text-sm text-red-600">Failed</div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">{selectedCampaign.clicked}</div>
                      <div className="text-sm text-blue-600">Clicked</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Message Content */}
              <div>
                <Label className="text-sm font-medium">Message Content</Label>
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm">{selectedCampaign.message}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" className="flex-1">
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate Campaign
                </Button>
                {selectedCampaign.status === "draft" && (
                  <Button className="flex-1">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send Now
                  </Button>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

const Label = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => (
  <label className={`block text-sm font-medium text-gray-700 ${className}`}>{children}</label>
);

export default CampaignHistory;
