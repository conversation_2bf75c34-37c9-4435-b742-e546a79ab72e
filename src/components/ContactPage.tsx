import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Phone, Mail, MapPin, MessageSquare, Send } from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";
import emailjs from "@emailjs/browser";

interface ContactPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const ContactPage = ({ onBack, onAuth, onNavigate }: ContactPageProps) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    subject: "",
    message: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const serviceId = "service_d3hm10i";
    const templateId = "template_upillqi";
    const publicKey = "bXa45xWnLU5eTEzlV";

    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      company: formData.company,
      phone: formData.phone,
      subject: formData.subject,
      message: formData.message,
      to_name: "Wispr SMS Team", // Or any recipient name you want
    };

    try {
      await emailjs.send(serviceId, templateId, templateParams, publicKey);
      console.log("Email sent successfully!");
      alert("Your message has been sent successfully!");
      setFormData({
        name: "",
        email: "",
        company: "",
        phone: "",
        subject: "",
        message: "",
      });
    } catch (error) {
      console.error("Failed to send email:", error);
      alert("Failed to send your message. Please try again later.");
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        onNavigate={onNavigate}
        onAuth={onAuth}
        currentPage="contact"
      />

      {/* Hero Section with Background Animation */}
      <section className="relative bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-[#4aa5af]/20 to-[#368f98]/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-20 right-0 w-96 h-96 bg-gradient-to-bl from-[#2d7a84]/20 to-[#4aa5af]/20 rounded-full blur-3xl animate-pulse animation-delay-1000"></div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-white">
            Get in Touch
            <span className="block text-white">With Our Team</span>
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Have questions about Wispr SMS? We're here to help you succeed with
            your SMS marketing campaigns.
          </p>
        </div>
      </section>

      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            {/* Contact Information */}
            <div className="lg:col-span-1">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Contact Information
                </h2>
                <p className="text-gray-600 mb-8">
                  Our team is here to help you succeed with your SMS marketing
                  campaigns. Reach out through any of these channels.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start">
                  <Mail className="h-6 w-6 text-[#368f98] mt-1 mr-4 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Email</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-sm text-gray-500">
                      We'll respond within 24 hours
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-6 w-6 text-[#368f98] mt-1 mr-4 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Phone</h3>
                    <p className="text-gray-600">(+233) 0534806662</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-[#368f98] mt-1 mr-4 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Office</h3>
                    <p className="text-gray-600">Spintex, Accra Ghana</p>
                  </div>
                </div>
              </div>

              {/* Support Options */}
              <Card className="mt-8">
                <CardHeader>
                  <CardTitle className="text-lg">
                    Need Immediate Help?
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    className="w-full justify-start bg-[#368f98] hover:bg-[#2d7a84] text-white"
                    onClick={() =>
                      window.open("https://wa.me/233534806662", "_blank")
                    }
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Chat on WhatsApp
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl sm:text-2xl">
                    Send us a Message
                  </CardTitle>
                  <CardDescription>
                    Fill out the form below and we'll get back to you as soon as
                    possible.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          id="name"
                          name="name"
                          type="text"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="company">Company</Label>
                        <Input
                          id="company"
                          name="company"
                          type="text"
                          value={formData.company}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="subject">Subject *</Label>
                      <Input
                        id="subject"
                        name="subject"
                        type="text"
                        required
                        value={formData.subject}
                        onChange={handleInputChange}
                        placeholder="How can we help you?"
                      />
                    </div>

                    <div>
                      <Label htmlFor="message">Message *</Label>
                      <Textarea
                        id="message"
                        name="message"
                        required
                        rows={6}
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Tell us more about your needs..."
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full sm:w-auto bg-[#368f98] hover:bg-[#2d7a84]"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Quick answers to common questions about our platform
            </p>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "How quickly can I get started?",
                answer:
                  "You can start sending SMS campaigns within minutes of signing up. Our onboarding process is designed to get you up and running quickly.",
              },
              {
                question: "Do you provide training and support?",
                answer:
                  "Yes! We offer comprehensive onboarding, training materials, and ongoing support to ensure your success with our platform.",
              },
              {
                question: "Can you help with campaign strategy?",
                answer:
                  "Absolutely. Our customer success team can provide guidance on best practices, campaign optimization, and SMS marketing strategies.",
              },
              {
                question: "What integrations do you support?",
                answer:
                  "We integrate with popular CRM systems, e-commerce platforms, and marketing tools. Contact us to discuss your specific integration needs.",
              },
            ].map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default ContactPage;
