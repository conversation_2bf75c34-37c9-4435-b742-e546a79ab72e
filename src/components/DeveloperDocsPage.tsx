import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Code,
  Send,
  Webhook,
  Shield,
  Clock,
  MessageSquare,
} from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";

interface DeveloperDocsPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const DeveloperDocsPage = ({
  onBack,
  onAuth,
  onNavigate,
}: DeveloperDocsPageProps) => {
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        onNavigate={onNavigate}
        onAuth={handleAuth}
        currentPage="docs"
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-white">
            Developer Documentation
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Integrate SMS capabilities into your applications with our powerful
            RESTful API.
          </p>
        </div>
      </section>

      {/* Quick Start Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Quick Start
            </h2>
            <p className="text-lg text-gray-600">
              Get started with our SMS API in just a few simple steps.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Authentication Card */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Shield className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-xl">Authentication</CardTitle>
                <CardDescription>
                  Securely authenticate your requests using API keys.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Include your API key in the <code>Authorization</code> header:
                </p>
                <pre className="bg-gray-100 rounded-md p-2 mt-2 text-xs">
                  <code>Authorization: Bearer YOUR_API_KEY</code>
                </pre>
              </CardContent>
            </Card>

            {/* Sending SMS Card */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Send className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-xl">Sending SMS</CardTitle>
                <CardDescription>
                  Send SMS messages to any mobile number worldwide.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Make a POST request to the <code>/messages</code> endpoint:
                </p>
                <pre className="bg-gray-100 rounded-md p-2 mt-2 text-xs">
                  <code>
                    POST /messages
                    <br />
                    Content-Type: application/json
                    <br />
                    <br />
                    {`{
                      "to": "+1234567890",
                      "body": "Hello from SMS Pro!"
                    }`}
                  </code>
                </pre>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* API Documentation Section */}
      <section className="bg-gray-50 py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              API Documentation
            </h2>
            <p className="text-lg text-gray-600">
              Explore our comprehensive API documentation for detailed
              information on endpoints, parameters, and response codes.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Endpoints Card */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Code className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-xl">Endpoints</CardTitle>
                <CardDescription>
                  List of available API endpoints.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside text-sm text-gray-600">
                  <li>
                    <code>/messages</code> - Send SMS messages
                  </li>
                  <li>
                    <code>/contacts</code> - Manage contacts
                  </li>
                  <li>
                    <code>/reports</code> - Get campaign reports
                  </li>
                  <li>
                    <code>/webhooks</code> - Configure webhooks
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Webhooks Card */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Webhook className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-xl">Webhooks</CardTitle>
                <CardDescription>
                  Receive real-time updates on message status and delivery.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Configure webhooks to receive POST requests when message
                  statuses change:
                </p>
                <ul className="list-disc list-inside text-sm text-gray-600">
                  <li>
                    <code>message.sent</code> - Message has been sent
                  </li>
                  <li>
                    <code>message.delivered</code> - Message has been delivered
                  </li>
                  <li>
                    <code>message.failed</code> - Message delivery failed
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default DeveloperDocsPage;
