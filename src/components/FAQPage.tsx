import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Navigation from "./Navigation";
import Footer from "./Footer";
import TopHeader from "./TopHeader";

interface FAQPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const FAQPage = ({ onBack, onAuth, onNavigate }: FAQPageProps) => {
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  const faqs = [
    {
      question: "Can I change my plan anytime?",
      answer:
        "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and billing is prorated.",
    },
    {
      question: "What happens if I exceed my SMS credits?",
      answer:
        "You can purchase additional credits at ₵0.50 per SMS, or upgrade to a higher plan for better rates.",
    },
    {
      question: "Is there a setup fee?",
      answer:
        "No setup fees. You only pay for your monthly plan and any additional SMS credits you use.",
    },
    {
      question: "Do you offer refunds?",
      answer:
        "We offer a 30-day money-back guarantee for new customers. No questions asked.",
    },
    {
      question: "What delivery rates can I expect?",
      answer:
        "We maintain a 99%+ delivery rate across all major networks in Ghana and internationally.",
    },
    {
      question: "How do I integrate the API?",
      answer:
        "Our REST API is simple to integrate. Check our developer documentation for detailed guides and code examples.",
    },
    {
      question: "Is my data secure?",
      answer:
        "Yes, we use enterprise-grade security with encryption and comply with GDPR regulations.",
    },
    {
      question: "What support options are available?",
      answer:
        "We offer 24/7 email support for all plans, with priority support and phone support for higher tier plans.",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Navigation
        onNavigate={onNavigate}
        onAuth={handleAuth}
        currentPage="faq"
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
            Frequently Asked
            <span className="block text-white">Questions</span>
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Find answers to common questions about SMS Pro features, pricing,
            and integration.
          </p>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-3 text-lg">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Still Have Questions?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Our support team is here to help you get started with SMS Pro.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => onNavigate("contact")}
              className="bg-[#368f98] hover:bg-[#2d7a84]"
            >
              Contact Support
            </Button>
            <Button
              size="lg"
              onClick={handleAuth}
              variant="outline"
              className="border-[#368f98] text-[#368f98] hover:bg-[#368f98] hover:text-white"
            >
              Get Started
            </Button>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default FAQPage;
