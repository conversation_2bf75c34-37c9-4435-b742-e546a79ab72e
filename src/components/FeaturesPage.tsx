
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, Users, BarChart3, Send, Shield, Clock, CheckCircle, Zap, Globe, HeadphonesIcon, Database, Smartphone } from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";

interface FeaturesPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const FeaturesPage = ({ onBack, onAuth, onNavigate }: FeaturesPageProps) => {
  const features = [
    {
      icon: Users,
      title: "Advanced Contact Management",
      description: "Import, organize, and segment your contacts with powerful filtering, grouping, and tagging capabilities. Support for CSV imports and custom fields.",
      highlights: ["Unlimited contacts", "Custom segmentation", "Duplicate detection", "Contact scoring"]
    },
    {
      icon: MessageSquare,
      title: "Smart SMS Composer",
      description: "Create engaging messages with our intuitive editor featuring templates, personalization tokens, and real-time character counting with multi-part SMS support.",
      highlights: ["Rich text editor", "Message templates", "Personalization", "Unicode support"]
    },
    {
      icon: BarChart3,
      title: "Comprehensive Analytics",
      description: "Track delivery rates, open rates, click-through rates, and conversion metrics with detailed reporting and real-time campaign monitoring.",
      highlights: ["Real-time tracking", "Conversion analytics", "A/B testing", "Custom reports"]
    },
    {
      icon: Clock,
      title: "Intelligent Scheduling",
      description: "Schedule campaigns for optimal delivery times, set up automated drip campaigns, and manage time zones for global reach.",
      highlights: ["Smart timing", "Drip campaigns", "Timezone support", "Recurring messages"]
    },
    {
      icon: Shield,
      title: "Compliance & Security",
      description: "GDPR compliant with built-in opt-out management, data encryption, and comprehensive audit trails for complete peace of mind.",
      highlights: ["GDPR compliance", "Auto opt-out", "Data encryption", "Audit trails"]
    },
    {
      icon: Send,
      title: "Enterprise Delivery",
      description: "99.9% delivery rates with our premium carrier network, redundant routing, and real-time delivery confirmations.",
      highlights: ["99.9% delivery", "Global reach", "Redundant routing", "Delivery receipts"]
    },
    {
      icon: Zap,
      title: "API Integration",
      description: "Powerful REST API for seamless integration with your existing systems, webhooks for real-time events, and extensive documentation.",
      highlights: ["RESTful API", "Webhooks", "SDKs available", "Rate limiting"]
    },
    {
      icon: Globe,
      title: "Global Coverage",
      description: "Send messages to 200+ countries with local number support, international formatting, and regional compliance handling.",
      highlights: ["200+ countries", "Local numbers", "Regional compliance", "Multi-language"]
    },
    {
      icon: HeadphonesIcon,
      title: "24/7 Expert Support",
      description: "Dedicated customer success team with live chat, phone support, and comprehensive knowledge base to help you succeed.",
      highlights: ["24/7 availability", "Live chat", "Phone support", "Onboarding help"]
    },
    {
      icon: Database,
      title: "Data Management",
      description: "Secure cloud storage with automatic backups, data export capabilities, and integration with popular CRM systems.",
      highlights: ["Cloud storage", "Auto backups", "Data export", "CRM integration"]
    },
    {
      icon: Smartphone,
      title: "Mobile Optimization",
      description: "Fully responsive platform with mobile apps for iOS and Android, allowing you to manage campaigns on the go.",
      highlights: ["Mobile apps", "Responsive design", "Offline access", "Push notifications"]
    },
    {
      icon: CheckCircle,
      title: "Quality Assurance",
      description: "Advanced spam filtering, content optimization suggestions, and delivery optimization to ensure maximum engagement.",
      highlights: ["Spam filtering", "Content optimization", "Delivery optimization", "Engagement scoring"]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navigation onNavigate={onNavigate} onAuth={onAuth} currentPage="features" />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-white">
            Powerful Features for
            <span className="block text-white">Modern SMS Marketing</span>
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Everything you need to create, send, and optimize SMS campaigns that drive results. 
            Professional tools trusted by thousands of businesses worldwide.
          </p>
          <Button size="lg" onClick={onAuth} className="bg-white text-[#368f98] hover:bg-gray-100">
            Start Free Trial
          </Button>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Complete SMS Marketing Solution</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From contact management to detailed analytics, our platform provides all the tools you need to succeed.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <feature.icon className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4" />
                  <CardTitle className="text-lg sm:text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-sm sm:text-base">{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.highlights.map((highlight, i) => (
                      <li key={i} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {highlight}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-[#368f98] text-white py-16 sm:py-20">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-white">Ready to Transform Your Marketing?</h2>
          <p className="text-lg sm:text-xl text-white mb-8">
            Join thousands of businesses using SMS Blaster Pro to reach their customers effectively.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" onClick={onAuth} className="bg-white text-[#368f98] hover:bg-gray-100">
              Start Free Trial
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-[#368f98]">
              Schedule Demo
            </Button>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default FeaturesPage;
