import {
  MessageSquare,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
} from "lucide-react";
interface FooterProps {
  onNavigate: (page: string) => void;
}
const Footer = ({ onNavigate }: FooterProps) => {
  return (
    <footer className="bg-[#368f98] text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
          <div className="text-center sm:text-left">
            <div className="flex items-center mb-4 justify-center sm:justify-start">
              <MessageSquare className="h-6 w-6 text-white" />
              <span className="ml-2 font-bold text-white">Wispr SMS</span>
            </div>
            <p className="text-white/80 text-sm mb-4">
              Professional SMS marketing platform for businesses of all sizes.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4 justify-center sm:justify-start">
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="https://linkedin.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/80 hover:text-white transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="https://tiktok.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white/80 hover:text-white transition-colors"
              >
                <svg
                  className="h-5 w-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
                </svg>
              </a>
            </div>
          </div>

          <div className="text-center sm:text-left">
            <h3 className="font-semibold text-white mb-4">Support</h3>
            <ul className="space-y-2 text-white/80 text-sm">
              <li>
                <button
                  onClick={() => onNavigate("docs")}
                  className="hover:text-white transition-colors"
                >
                  API Documentation
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate("contact")}
                  className="hover:text-white transition-colors"
                >
                  Help Support
                </button>
              </li>
            </ul>
          </div>

          <div className="text-center sm:text-left">
            <h3 className="font-semibold text-white mb-4">Company</h3>
            <ul className="space-y-2 text-white/80 text-sm">
              <li>
                <button
                  onClick={() => onNavigate("about")}
                  className="hover:text-white transition-colors"
                >
                  About Us
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate("privacy")}
                  className="hover:text-white transition-colors"
                >
                  Privacy Policy
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate("terms")}
                  className="hover:text-white transition-colors"
                >
                  Terms of Service
                </button>
              </li>
            </ul>
          </div>

          <div className="text-center sm:text-left">
            <h3 className="font-semibold text-white mb-4">Contact</h3>
            <div className="space-y-2 text-white/80 text-sm">
              <p>Spintex, Accra Ghana</p>
              <p>(+233) 0534806662</p>
              <p><EMAIL></p>
            </div>
          </div>
        </div>
        <div className="border-t border-white/20 mt-6 sm:mt-8 pt-6 sm:pt-8 text-center text-white/80">
          <p className="text-sm">© 2025 Wispr SMS. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
