import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useState, useEffect } from "react";

interface NavigationProps {
  onNavigate: (page: string) => void;
  onAuth: () => void;
  currentPage?: string;
  showAuthButton?: boolean;
}

const Navigation = ({
  onNavigate,
  onAuth,
  currentPage = "home",
  showAuthButton = true,
}: NavigationProps) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  const handleNavigation = (page: string) => {
    // Add smooth transition effect
    document.body.style.opacity = "0.8";
    setTimeout(() => {
      onNavigate(page);
      document.body.style.opacity = "1";
    }, 150);
    setMobileMenuOpen(false);
  };
  const handleLogoClick = () => {
    document.body.style.opacity = "0.8";
    setTimeout(() => {
      onNavigate("home");
      document.body.style.opacity = "1";
    }, 150);
    setMobileMenuOpen(false);
  };
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <div className="flex items-center">
            <button
              onClick={handleLogoClick}
              className="flex items-center group transition-all duration-300"
            >
              <div className="relative">
                <img
                  src="/uploads/3788df22-a283-471a-bc01-6ace75be7d94.png"
                  alt="Wispr SMS"
                  className="h-12 w-12 transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <span
                className={`ml-3 text-xl font-bold transition-colors duration-300 ${
                  isScrolled ? "text-gray-900" : "text-white"
                } group-hover:text-primary`}
              >
                Wispr SMS
              </span>
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {[
              {
                id: "home",
                label: "Home",
              },
              {
                id: "solutions",
                label: "Solutions",
              },
              {
                id: "pricing",
                label: "Pricing",
              },
              {
                id: "docs",
                label: "Developers",
              },
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  if (item.id === "docs") {
                    window.open(
                      "https://documenter.getpostman.com/view/7705958/Uyr7Hydn",
                      "_blank"
                    );
                  } else {
                    handleNavigation(item.id);
                  }
                }}
                className={`relative px-3 py-2 text-sm font-medium transition-all duration-300 rounded-lg ${
                  currentPage === item.id
                    ? "text-primary bg-primary/10"
                    : isScrolled
                    ? "text-gray-700 hover:text-primary hover:bg-primary/5"
                    : "text-white/90 hover:text-white hover:bg-white/10"
                }`}
              >
                {item.label}
              </button>
            ))}
            {showAuthButton && (
              <Button
                onClick={handleAuth}
                className="ml-4 btn-modern bg-primary hover:bg-primary/90 text-white border-0"
              >
                Get Started
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className={`transition-all duration-300 ${
                isScrolled
                  ? "hover:bg-primary/10 text-gray-700"
                  : "hover:bg-white/10 text-white"
              }`}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 z-50 animate-fade-in">
            <div className="px-4 pt-4 pb-6 space-y-3 bg-white/95 backdrop-blur-md border-t border-gray-100 shadow-xl">
              {[
                {
                  id: "home",
                  label: "Home",
                },
                {
                  id: "solutions",
                  label: "Solutions",
                },
                {
                  id: "pricing",
                  label: "Pricing",
                },
                {
                  id: "docs",
                  label: "Developers",
                },
              ].map((item) => (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={`w-full justify-start text-left font-medium transition-all duration-300 rounded-lg ${
                    currentPage === item.id
                      ? "text-primary bg-primary/10"
                      : "text-gray-700 hover:text-primary hover:bg-primary/5"
                  }`}
                  onClick={() => {
                    if (item.id === "docs") {
                      window.open(
                        "https://documenter.getpostman.com/view/7705958/Uyr7Hydn",
                        "_blank"
                      );
                    } else {
                      handleNavigation(item.id);
                    }
                  }}
                >
                  {item.label}
                </Button>
              ))}
              {showAuthButton && (
                <Button
                  className="w-full mt-4 btn-modern bg-primary hover:bg-primary/90 text-white"
                  onClick={handleAuth}
                >
                  Get Started
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
export default Navigation;
