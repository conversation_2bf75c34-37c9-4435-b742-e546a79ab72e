import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useState } from "react";
interface NavigationProps {
  onNavigate: (page: string) => void;
  onAuth: () => void;
  currentPage?: string;
  showAuthButton?: boolean;
}
const Navigation = ({
  onNavigate,
  onAuth,
  currentPage = "home",
  showAuthButton = true,
}: NavigationProps) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const handleNavigation = (page: string) => {
    // Add smooth transition effect
    document.body.style.opacity = "0.8";
    setTimeout(() => {
      onNavigate(page);
      document.body.style.opacity = "1";
    }, 150);
    setMobileMenuOpen(false);
  };
  const handleLogoClick = () => {
    document.body.style.opacity = "0.8";
    setTimeout(() => {
      onNavigate("home");
      document.body.style.opacity = "1";
    }, 150);
    setMobileMenuOpen(false);
  };
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  // Animated background for navbar
  const navBackground = (
    <div className="absolute inset-0 -z-10">
      <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-[#368f98]/10 to-[#4aa5af]/10 rounded-full blur-2xl animate-pulse"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-[#4aa5af]/10 to-[#368f98]/10 rounded-full blur-2xl animate-pulse animation-delay-1000"></div>
    </div>
  );
  return (
    <nav className="bg-white/80 backdrop-blur-sm shadow-sm relative">
      {navBackground}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <button
              onClick={handleLogoClick}
              className="flex items-center hover:opacity-80 transition-all duration-300 hover:scale-105"
            >
              <div className="relative">
                <img
                  src="/uploads/3788df22-a283-471a-bc01-6ace75be7d94.png"
                  alt="Wispr SMS"
                  className="h-24 w-24 sm:h-32 sm:w-32"
                />
              </div>
              <span className="ml-1 text-lg sm:text-xl font-bold text-gray-900">
                {/*-- Logo Name--*/}
              </span>
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-1">
            {[
              {
                id: "home",
                label: "Home",
              },
              {
                id: "solutions",
                label: "Solutions",
              },
              {
                id: "pricing",
                label: "Pricing",
              },
              {
                id: "docs",
                label: "Developers",
              },
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  if (item.id === "docs") {
                    window.open(
                      "https://documenter.getpostman.com/view/7705958/Uyr7Hydn",
                      "_blank"
                    );
                  } else {
                    handleNavigation(item.id);
                  }
                }}
                className={`relative px-4 py-2 text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                  currentPage === item.id
                    ? "text-[#368f98]"
                    : "text-gray-700 hover:text-[#368f98]"
                }`}
              >
                {item.label}
                {currentPage === item.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#368f98] animate-scale-in"></div>
                )}
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#368f98] scale-x-0 transition-transform duration-300 origin-left hover:scale-x-100"></div>
              </button>
            ))}
            {showAuthButton && (
              <Button
                onClick={handleAuth}
                className="ml-8 bg-[#368f98] hover:bg-[#2d7a84] transition-all duration-300 hover:scale-105"
              >
                Signup/Login
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="hover:bg-[#368f98]/10 transition-all duration-300"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 z-50 animate-fade-in">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white/95 backdrop-blur-sm border-t shadow-lg">
              {[
                {
                  id: "home",
                  label: "Home",
                },
                {
                  id: "solutions",
                  label: "Solutions",
                },
                {
                  id: "pricing",
                  label: "Pricing",
                },
                {
                  id: "docs",
                  label: "Developers",
                },
              ].map((item) => (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={`w-full justify-start hover:bg-[#368f98]/10 transition-all duration-300 ${
                    currentPage === item.id
                      ? "text-[#368f98] bg-[#368f98]/10"
                      : ""
                  }`}
                  onClick={() => {
                    if (item.id === "docs") {
                      window.open(
                        "https://documenter.getpostman.com/view/7705958/Uyr7Hydn",
                        "_blank"
                      );
                    } else {
                      handleNavigation(item.id);
                    }
                  }}
                >
                  {item.label}
                </Button>
              ))}
              {showAuthButton && (
                <Button
                  className="w-full bg-[#368f98] hover:bg-[#2d7a84] transition-all duration-300"
                  onClick={handleAuth}
                >
                  Signup/Login
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
export default Navigation;
