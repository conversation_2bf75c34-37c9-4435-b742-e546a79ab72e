
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, MessageSquare, Users, Send } from "lucide-react";

interface NewCampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewCampaignModal = ({ isOpen, onClose }: NewCampaignModalProps) => {
  const [campaignData, setCampaignData] = useState({
    name: '',
    message: '',
    contactGroup: '',
    scheduledDate: '',
    scheduledTime: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Creating new campaign:', campaignData);
    // Here you would normally send the data to your backend
    onClose();
    setCampaignData({
      name: '',
      message: '',
      contactGroup: '',
      scheduledDate: '',
      scheduledTime: ''
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setCampaignData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
            Create New SMS Campaign
          </DialogTitle>
          <DialogDescription>
            Set up your SMS campaign details and schedule delivery.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="campaignName">Campaign Name</Label>
              <Input
                id="campaignName"
                placeholder="e.g., Holiday Sale Alert"
                value={campaignData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="message">Message Content</Label>
              <Textarea
                id="message"
                placeholder="Type your SMS message here..."
                value={campaignData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                rows={4}
                maxLength={160}
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                {campaignData.message.length}/160 characters
              </p>
            </div>

            <div>
              <Label htmlFor="contactGroup">Target Audience</Label>
              <Select value={campaignData.contactGroup} onValueChange={(value) => handleInputChange('contactGroup', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select contact group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Contacts</SelectItem>
                  <SelectItem value="customers">Customers</SelectItem>
                  <SelectItem value="prospects">Prospects</SelectItem>
                  <SelectItem value="vip">VIP Members</SelectItem>
                  <SelectItem value="newsletter">Newsletter Subscribers</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="scheduledDate">Schedule Date (Optional)</Label>
                <Input
                  id="scheduledDate"
                  type="date"
                  value={campaignData.scheduledDate}
                  onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="scheduledTime">Schedule Time</Label>
                <Input
                  id="scheduledTime"
                  type="time"
                  value={campaignData.scheduledTime}
                  onChange={(e) => handleInputChange('scheduledTime', e.target.value)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              <Send className="h-4 w-4 mr-2" />
              {campaignData.scheduledDate ? 'Schedule Campaign' : 'Send Now'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default NewCampaignModal;
