import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, Star } from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";
interface PricingPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}
const PricingPage = ({ onBack, onAuth, onNavigate }: PricingPageProps) => {
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };
  return (
    <div className="min-h-screen bg-white">
      <Navigation
        onNavigate={onNavigate}
        onAuth={handleAuth}
        currentPage="pricing"
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6">
            Simple, Transparent
            <span className="block text-white">Pricing</span>
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Choose the perfect plan for your SMS marketing needs. No hidden
            fees, no surprises.
          </p>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "STANDARD",
                price: "GHS12.00",
                period: "Non Expiry",
                description: "Perfect for small businesses starting with SMS",
                features: [
                  "200 Total SMS",
                  "0.06GHS / SMS",
                  "Import/Export Contact List",
                  "Sender ID Verification",
                  "API Access",
                  "24/7 Customer Service",
                ],
                popular: false,
                highlight: false,
              },
              {
                name: "PREMIUM",
                price: "GHS60.00",
                period: "Non Expiry",
                description: "Most popular for growing businesses",
                features: [
                  "1000 Total SMS",
                  "0.06GHS / SMS",
                  "Import/Export Contact List",
                  "Sender ID Verification",
                  "API Access",
                  "24/7 Customer Service",
                ],
                popular: true,
                highlight: true,
              },
              {
                name: "EXECUTIVE",
                price: "GHS600.00",
                period: "Non Expiry",
                description: "For large organizations with high volume needs",
                features: [
                  "10,000 Total SMS",
                  "0.06GHS / SMS",
                  "Import/Export Contact List",
                  "Sender ID Verification",
                  "API Access",
                  "24/7 Customer Service",
                ],
                popular: false,
                highlight: false,
              },
            ].map((plan, index) => (
              <Card
                key={index}
                className={`relative ${
                  plan.highlight
                    ? "border-[#368f98] border-2 shadow-lg scale-105"
                    : "hover:shadow-lg"
                } transition-all duration-300`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#368f98] text-white px-6 py-2 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-[#368f98]">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 ml-1">{plan.period}</span>
                  </div>
                  <CardDescription className="text-base">
                    {plan.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-[#368f98] mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${
                      plan.highlight
                        ? "bg-[#368f98] hover:bg-[#2d7a84] text-white"
                        : "border-[#368f98] text-[#368f98] hover:bg-[#368f98] hover:text-white"
                    }`}
                    variant={plan.highlight ? "default" : "outline"}
                    size="lg"
                    onClick={handleAuth}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Compare Plans
            </h2>
            <p className="text-lg text-gray-600">
              See what's included in each plan
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">
                    Features
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">
                    Starter
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">
                    Professional
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">
                    Enterprise
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {[
                  {
                    feature: "SMS Credits/Month",
                    starter: "1,000",
                    professional: "10,000",
                    enterprise: "Unlimited",
                  },
                  {
                    feature: "Contact Management",
                    starter: "✓",
                    professional: "✓",
                    enterprise: "✓",
                  },
                  {
                    feature: "Analytics",
                    starter: "Basic",
                    professional: "Advanced",
                    enterprise: "Custom",
                  },
                  {
                    feature: "API Access",
                    starter: "✗",
                    professional: "✓",
                    enterprise: "✓",
                  },
                  {
                    feature: "Support",
                    starter: "Email",
                    professional: "Priority",
                    enterprise: "24/7 Phone",
                  },
                  {
                    feature: "Custom Sender ID",
                    starter: "✗",
                    professional: "✓",
                    enterprise: "✓",
                  },
                  {
                    feature: "A/B Testing",
                    starter: "✗",
                    professional: "✓",
                    enterprise: "✓",
                  },
                  {
                    feature: "Dedicated Manager",
                    starter: "✗",
                    professional: "✗",
                    enterprise: "✓",
                  },
                ].map((row, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      {row.feature}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-center">
                      {row.starter}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-center">
                      {row.professional}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-center">
                      {row.enterprise}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* CTA Section with grey background */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Join thousands of businesses already using Wispr SMS to grow their
            customer engagement.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={handleAuth}
              className="bg-[#368f98] hover:bg-[#2d7a84]"
            >
              Start Your Free Trial
            </Button>
            <Button
              size="lg"
              onClick={() => onNavigate("contact")}
              variant="outline"
              className="border-[#368f98] text-[#368f98] hover:bg-[#368f98] hover:text-white"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};
export default PricingPage;
