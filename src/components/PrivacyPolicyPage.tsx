import Navigation from "./Navigation";
import Footer from "./Footer";

interface PrivacyPolicyPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const PrivacyPolicyPage = ({
  onBack,
  onAuth,
  onNavigate,
}: PrivacyPolicyPageProps) => {
  return (
    <div className="min-h-screen bg-white">
      <Navigation
        onNavigate={onNavigate}
        onAuth={onAuth}
        currentPage="privacy"
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-white">
            Privacy Policy
          </h1>
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Your privacy is important to us. Learn how we protect and handle
            your data.
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose prose-lg max-w-none">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Information We Collect
            </h2>
            <p className="text-gray-600 mb-6">
              We collect information you provide directly to us, such as when
              you create an account, use our services, or contact us for
              support.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 mt-8">
              How We Use Your Information
            </h2>
            <p className="text-gray-600 mb-6">
              We use the information we collect to provide, maintain, and
              improve our services, process transactions, and communicate with
              you.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 mt-8">
              Data Security
            </h2>
            <p className="text-gray-600 mb-6">
              We implement appropriate security measures to protect your
              personal information against unauthorized access, alteration,
              disclosure, or destruction.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 mt-8">
              Contact Us
            </h2>
            <p className="text-gray-600 mb-6">
              If you have any questions about this Privacy Policy, please
              contact <NAME_EMAIL>
            </p>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default PrivacyPolicyPage;
