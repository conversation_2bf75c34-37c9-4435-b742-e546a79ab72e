
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Calendar, Send, Users, MessageSquare, Clock, Smartphone } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const SmsComposer = () => {
  const [message, setMessage] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [selectedGroup, setSelectedGroup] = useState("");
  const [campaignName, setCampaignName] = useState("");
  const [scheduleDate, setScheduleDate] = useState("");
  const [scheduleTime, setScheduleTime] = useState("");
  const { toast } = useToast();

  const messageLength = message.length;
  const smsCount = Math.ceil(messageLength / 160);
  const remainingChars = 160 - (messageLength % 160);

  const templates = [
    { id: "welcome", name: "Welcome Message", content: "Welcome to our service! We're excited to have you on board. Reply STOP to opt out." },
    { id: "promotion", name: "Promotional Offer", content: "🎉 Special offer just for you! Get 20% off your next purchase. Use code SAVE20. Valid until [date]." },
    { id: "reminder", name: "Appointment Reminder", content: "Hi [name], this is a reminder about your appointment on [date] at [time]. Reply YES to confirm." },
    { id: "shipping", name: "Shipping Update", content: "Your order #[order] has shipped! Track it here: [link]. Expected delivery: [date]." },
  ];

  const contactGroups = [
    { id: "all", name: "All Contacts", count: 2847 },
    { id: "customers", name: "Customers", count: 1250 },
    { id: "prospects", name: "Prospects", count: 890 },
    { id: "vip", name: "VIP Customers", count: 145 },
  ];

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setMessage(template.content);
      setSelectedTemplate(templateId);
    }
  };

  const handleSendNow = () => {
    if (!message.trim() || !selectedGroup) {
      toast({
        title: "Error",
        description: "Please select a contact group and enter a message.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Campaign Sent!",
      description: `Your SMS campaign has been sent to ${contactGroups.find(g => g.id === selectedGroup)?.count} contacts.`,
    });

    // Reset form
    setMessage("");
    setSelectedTemplate("");
    setSelectedGroup("");
    setCampaignName("");
  };

  const handleSchedule = () => {
    if (!message.trim() || !selectedGroup || !scheduleDate || !scheduleTime) {
      toast({
        title: "Error",
        description: "Please fill in all required fields to schedule the campaign.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Campaign Scheduled!",
      description: `Your SMS campaign has been scheduled for ${scheduleDate} at ${scheduleTime}.`,
    });

    // Reset form
    setMessage("");
    setSelectedTemplate("");
    setSelectedGroup("");
    setCampaignName("");
    setScheduleDate("");
    setScheduleTime("");
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">SMS Composer</h2>
        <p className="text-muted-foreground">Create and send SMS campaigns to your contacts</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Composer */}
        <div className="lg:col-span-2 space-y-6">
          {/* Campaign Details */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Details</CardTitle>
              <CardDescription>Set up your SMS campaign</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="campaign-name">Campaign Name</Label>
                <Input
                  id="campaign-name"
                  placeholder="Enter campaign name..."
                  value={campaignName}
                  onChange={(e) => setCampaignName(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="contact-group">Contact Group</Label>
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select contact group" />
                  </SelectTrigger>
                  <SelectContent>
                    {contactGroups.map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{group.name}</span>
                          <Badge variant="secondary" className="ml-2">
                            {group.count} contacts
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Message Templates */}
          <Card>
            <CardHeader>
              <CardTitle>Message Templates</CardTitle>
              <CardDescription>Choose from pre-built templates or create your own</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {templates.map((template) => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate === template.id ? "default" : "outline"}
                    className="h-auto p-3 text-left justify-start"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground truncate">
                        {template.content.substring(0, 50)}...
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Message Composer */}
          <Card>
            <CardHeader>
              <CardTitle>Compose Message</CardTitle>
              <CardDescription>Write your SMS message</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="message">Message Content</Label>
                <Textarea
                  id="message"
                  placeholder="Type your SMS message here..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={6}
                  className="resize-none"
                />
                <div className="flex justify-between items-center mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <span>{messageLength} characters</span>
                    <span>{smsCount} SMS{smsCount > 1 ? 's' : ''}</span>
                    {messageLength > 160 && (
                      <span>{remainingChars} chars remaining in current SMS</span>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Smartphone className="h-4 w-4" />
                    <span>160 char limit per SMS</span>
                  </div>
                </div>
              </div>
              
              {/* Personalization Tags */}
              <div>
                <Label>Personalization Tags</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {['[name]', '[company]', '[date]', '[time]', '[order]', '[link]'].map((tag) => (
                    <Button
                      key={tag}
                      variant="outline"
                      size="sm"
                      onClick={() => setMessage(prev => prev + tag)}
                    >
                      {tag}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Options */}
          <Card>
            <CardHeader>
              <CardTitle>Send Options</CardTitle>
              <CardDescription>Choose when to send your campaign</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="schedule-date">Schedule Date</Label>
                  <Input
                    id="schedule-date"
                    type="date"
                    value={scheduleDate}
                    onChange={(e) => setScheduleDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="schedule-time">Schedule Time</Label>
                  <Input
                    id="schedule-time"
                    type="time"
                    value={scheduleTime}
                    onChange={(e) => setScheduleTime(e.target.value)}
                  />
                </div>
              </div>
              
              <Separator />
              
              <div className="flex gap-3">
                <Button onClick={handleSendNow} className="flex-1">
                  <Send className="h-4 w-4 mr-2" />
                  Send Now
                </Button>
                <Button onClick={handleSchedule} variant="outline" className="flex-1">
                  <Clock className="h-4 w-4 mr-2" />
                  Schedule Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          {/* Message Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                Message Preview
              </CardTitle>
              <CardDescription>How your message will appear on mobile</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-100 rounded-lg p-4 min-h-[200px]">
                <div className="bg-blue-500 text-white p-3 rounded-lg max-w-[250px] text-sm">
                  {message || "Your message will appear here..."}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  SMS • {new Date().toLocaleTimeString()}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Campaign Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Recipients:</span>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span className="font-medium">
                    {selectedGroup ? contactGroups.find(g => g.id === selectedGroup)?.count || 0 : 0}
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">SMS Count:</span>
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-4 w-4" />
                  <span className="font-medium">{smsCount}</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total SMS:</span>
                <span className="font-medium">
                  {selectedGroup ? (contactGroups.find(g => g.id === selectedGroup)?.count || 0) * smsCount : 0}
                </span>
              </div>
              {scheduleDate && scheduleTime && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Scheduled:</span>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span className="font-medium text-sm">
                      {scheduleDate} at {scheduleTime}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Tips */}
          <Card>
            <CardHeader>
              <CardTitle>SMS Best Practices</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>Keep messages under 160 characters when possible</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>Include clear call-to-action</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>Always provide opt-out instructions</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <span>Send during business hours (9 AM - 6 PM)</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SmsComposer;
