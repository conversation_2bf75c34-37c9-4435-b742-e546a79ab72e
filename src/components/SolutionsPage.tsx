import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  MessageSquare,
  Users,
  BarChart3,
  Shield,
  Clock,
  Zap,
  Target,
  Smartphone,
  Globe,
  HeadphonesIcon,
  DollarSign,
  TrendingUp,
} from "lucide-react";
import Navigation from "./Navigation";
import Footer from "./Footer";

interface SolutionsPageProps {
  onBack: () => void;
  onAuth: () => void;
  onNavigate: (page: string) => void;
}

const SolutionsPage = ({ onBack, onAuth, onNavigate }: SolutionsPageProps) => {
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        onNavigate={onNavigate}
        onAuth={handleAuth}
        currentPage="solutions"
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-[#368f98] to-[#2d7a84] text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-white">
            SMS Marketing Solutions
            <span className="block text-white">For Every Business</span>
          </h1> */}
          <p className="text-lg sm:text-xl text-white mb-8 max-w-3xl mx-auto">
            Discover how our platform can transform your customer communication
            across industries.
          </p>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Core Features
            </h2>
            <p className="text-lg text-gray-600">
              Explore the key features that make our SMS marketing platform
              stand out.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Zap className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Automation</CardTitle>
                <CardDescription>
                  Automate your SMS marketing with our powerful automation
                  tools.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card className="card-hover animate-fade-in-up animation-delay-700">
              <CardHeader className="text-center sm:text-left">
                <Users className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
                <CardTitle className="text-lg sm:text-xl">
                  Contact Management
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Import, organize, and segment your contacts with advanced
                  filtering and grouping options.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover animate-fade-in-up animation-delay-800">
              <CardHeader className="text-center sm:text-left">
                <MessageSquare className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
                <CardTitle className="text-lg sm:text-xl">
                  SMS Composer
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Create engaging messages with templates, personalization, and
                  real-time character counting. Send Bulk SMS messages to
                  thousands of recipients instantly.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover animate-fade-in-up animation-delay-900">
              <CardHeader className="text-center sm:text-left">
                <BarChart3 className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
                <CardTitle className="text-lg sm:text-xl">
                  Analytics & Reports
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Track delivery rates, engagement metrics, and campaign
                  performance with detailed analytics.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover animate-fade-in-up animation-delay-1000">
              <CardHeader className="text-center sm:text-left">
                <Clock className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
                <CardTitle className="text-lg sm:text-xl">
                  Scheduled Campaigns
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  Schedule your messages for optimal delivery times and automate
                  your marketing campaigns.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="card-hover animate-fade-in-up animation-delay-1100">
              <CardHeader className="text-center sm:text-left">
                <Shield className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
                <CardTitle className="text-lg sm:text-xl">
                  Compliance & Security
                </CardTitle>
                <CardDescription className="text-sm sm:text-base">
                  GDPR compliant with opt-out management and secure data
                  handling for peace of mind.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="bg-gray-50 py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Industry-Specific Solutions
            </h2>
            <p className="text-lg text-gray-600">
              Tailored SMS marketing strategies for every business type
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* Retail & E-commerce */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Target className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Retail & E-commerce</CardTitle>
                <CardDescription>
                  Drive sales with promotional campaigns, order confirmations,
                  and shipping updates.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Cart abandonment recovery</li>
                  <li>✓ Flash sale notifications</li>
                  <li>✓ Order tracking updates</li>
                  <li>✓ Customer loyalty programs</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Increase sales by 25%
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Healthcare */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <HeadphonesIcon className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Healthcare</CardTitle>
                <CardDescription>
                  Improve patient communication with appointment reminders and
                  health alerts.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Appointment reminders</li>
                  <li>✓ Prescription refill alerts</li>
                  <li>✓ Health tips and wellness</li>
                  <li>✓ Emergency notifications</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Reduce no-shows by 40%
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Education */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Globe className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Education</CardTitle>
                <CardDescription>
                  Keep students and parents informed with important
                  announcements and updates.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Class schedule changes</li>
                  <li>✓ Event reminders</li>
                  <li>✓ Emergency communications</li>
                  <li>✓ Grade notifications</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Improve engagement by 60%
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Real Estate */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Real Estate</CardTitle>
                <CardDescription>
                  Connect with clients through property alerts and appointment
                  scheduling.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Property viewing reminders</li>
                  <li>✓ Market updates</li>
                  <li>✓ New listing alerts</li>
                  <li>✓ Contract deadlines</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Boost lead conversion
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Services */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Zap className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Financial Services</CardTitle>
                <CardDescription>
                  Secure communication for account alerts and payment reminders.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Account balance alerts</li>
                  <li>✓ Payment due reminders</li>
                  <li>✓ Transaction notifications</li>
                  <li>✓ Security alerts</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Enhance security & trust
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Professional Services */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <BarChart3 className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Professional Services</CardTitle>
                <CardDescription>
                  Streamline client communication and appointment management.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600 mb-4">
                  <li>✓ Appointment confirmations</li>
                  <li>✓ Service reminders</li>
                  <li>✓ Follow-up surveys</li>
                  <li>✓ Promotional offers</li>
                </ul>
                <div className="bg-[#368f98]/10 p-3 rounded-lg">
                  <div className="text-sm font-medium text-[#368f98]">
                    Optimize client relations
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Use Cases
            </h2>
            <p className="text-lg text-gray-600">
              Explore various use cases for SMS marketing in your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <MessageSquare className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Promotions</CardTitle>
                <CardDescription>
                  Send promotional offers and discounts to your customers.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Reminders</CardTitle>
                <CardDescription>
                  Send appointment reminders and event notifications to your
                  customers.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <BarChart3 className="h-10 w-10 text-[#368f98] mb-4" />
                <CardTitle className="text-lg">Notifications</CardTitle>
                <CardDescription>
                  Keep your customers informed with important updates and
                  notifications.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      <Footer onNavigate={onNavigate} />
    </div>
  );
};

export default SolutionsPage;
