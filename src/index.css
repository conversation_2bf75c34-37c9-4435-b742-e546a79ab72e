@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System inspired by x.ai, scale.com, and cruip.com */

@layer base {
  :root {
    /* Modern neutral backgrounds with subtle warmth */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Card backgrounds with subtle elevation */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Modern primary color - sophisticated blue/purple */
    --primary: 238 69% 60%; /* #4F46E5 equivalent */
    --primary-foreground: 0 0% 100%;

    /* Soft secondary colors */
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    /* Muted colors for subtle elements */
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    /* Accent colors for highlights */
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Modern border and input styling */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 238 69% 60%;

    /* Increased border radius for modern look */
    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
      "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    transition: opacity 0.3s ease-in-out;
    line-height: 1.6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Inter", sans-serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    letter-spacing: -0.04em;
  }

  h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    letter-spacing: -0.03em;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
  }
}

/* Page transition animations */
.page-fade-in {
  animation: page-fade-in 0.6s ease-out;
}

@keyframes page-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom animations */
@keyframes scale-in {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.7s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.7s ease-out;
}

/* Staggered animation delays */
.animation-delay-100 {
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.animation-delay-200 {
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.animation-delay-300 {
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.animation-delay-500 {
  animation-delay: 0.5s;
  animation-fill-mode: both;
}

.animation-delay-750 {
  animation-delay: 0.75s;
  animation-fill-mode: both;
}

.animation-delay-1000 {
  animation-delay: 1s;
  animation-fill-mode: both;
}

.animation-delay-1250 {
  animation-delay: 1.25s;
  animation-fill-mode: both;
}

.animation-delay-1500 {
  animation-delay: 1.5s;
  animation-fill-mode: both;
}

.animation-delay-2000 {
  animation-delay: 2s;
  animation-fill-mode: both;
}

.animation-delay-2500 {
  animation-delay: 2.5s;
  animation-fill-mode: both;
}

.animation-delay-2750 {
  animation-delay: 2.75s;
  animation-fill-mode: both;
}

.animation-delay-3000 {
  animation-delay: 3s;
  animation-fill-mode: both;
}

/* Section fade-in animations */
.section-fade-in {
  opacity: 0;
  animation: fade-in-up 0.8s ease-out forwards;
}

/* Modern card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(79, 70, 229, 0.1);
}

/* Modern button hover effects */
.btn-modern {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.4);
}

/* Scroll-triggered animations */
@keyframes fade-in-up-modern {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in-modern {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-up-modern {
  animation: fade-in-up-modern 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in-modern {
  animation: scale-in-modern 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern gradient backgrounds */
.gradient-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-subtle {
  background: linear-gradient(
    135deg,
    rgba(79, 70, 229, 0.05) 0%,
    rgba(139, 92, 246, 0.05) 100%
  );
}
