
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  MessageSquare,
  Users,
  BarChart3,
  Send,
  Shield,
  Clock,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import { useState } from "react";
import Dashboard from "@/components/Dashboard";
import AuthPage from "@/components/AuthPage";
import PricingPage from "@/components/PricingPage";
import ContactPage from "@/components/ContactPage";
import SolutionsPage from "@/components/SolutionsPage";
import DeveloperDocsPage from "@/components/DeveloperDocsPage";
import AboutUsPage from "@/components/AboutUsPage";
import PrivacyPolicyPage from "@/components/PrivacyPolicyPage";
import TermsOfServicePage from "@/components/TermsOfServicePage";
import FAQPage from "@/components/FAQPage";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

const Index = () => {
  const [currentPage, setCurrentPage] = useState("home");
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };

  const handleLogin = () => {
    setIsAuthenticated(true);
    setCurrentPage("dashboard");
  };

  const handleBack = () => {
    setCurrentPage("home");
  };

  const navigateTo = (page: string) => {
    setCurrentPage(page);
  };

  // Show appropriate page based on current state
  if (currentPage === "dashboard" && isAuthenticated) {
    return (
      <div className="page-fade-in">
        <Dashboard
          onBack={handleBack}
          onNavigate={navigateTo}
          onAuth={handleAuth}
        />
      </div>
    );
  }

  if (currentPage === "auth") {
    return (
      <div className="page-fade-in">
        <AuthPage
          onBack={handleBack}
          onLogin={handleLogin}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "pricing") {
    return (
      <div className="page-fade-in">
        <PricingPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "contact") {
    return (
      <div className="page-fade-in">
        <ContactPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "solutions") {
    return (
      <div className="page-fade-in">
        <SolutionsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "docs") {
    return (
      <div className="page-fade-in">
        <DeveloperDocsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "about") {
    return (
      <div className="page-fade-in">
        <AboutUsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "privacy") {
    return (
      <div className="page-fade-in">
        <PrivacyPolicyPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "terms") {
    return (
      <div className="page-fade-in">
        <TermsOfServicePage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  if (currentPage === "faq") {
    return (
      <div className="page-fade-in">
        <FAQPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }

  // Homepage content
  return (
    <div className="min-h-screen bg-white page-fade-in">
      <div className="animate-fade-in animation-delay-100">
        <Navigation onNavigate={navigateTo} onAuth={handleAuth} />
      </div>

      {/* Professional hero section with brand colors and animations */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-gray-50/50 to-white">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-brand-teal/20 to-brand-orange/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-brand-orange/20 to-brand-teal/20 rounded-full blur-3xl animate-pulse animation-delay-1000"></div>
        </div>

        <div className="max-w-7xl mx-auto container-padding section-padding relative z-10">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 mb-8 leading-[0.9]">
              <span className="block mb-2">Smart & Scalable</span>
              <span className="block gradient-text-animated mb-2">
                SMS Marketing
              </span>
              <span className="block">Solutions</span>
            </h1>
            <p className="text-professional-large mb-12 max-w-2xl mx-auto">
              Reach thousands of customers instantly with our professional SMS
              marketing platform. Create, send, and track campaigns with
              enterprise-grade reliability and intelligent automation.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                size="lg"
                onClick={handleAuth}
                className="btn-professional text-lg px-10 py-5 bg-gradient-to-r from-brand-teal to-brand-orange hover:from-brand-teal/90 hover:to-brand-orange/90 text-white border-0 shadow-professional-lg rounded-2xl font-semibold"
              >
                Get Started Free
                <Send className="ml-3 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => navigateTo("pricing")}
                className="btn-professional text-lg px-10 py-5 border-2 border-brand-teal/30 text-brand-teal hover:border-brand-teal hover:bg-brand-teal/5 bg-white/80 backdrop-blur-sm shadow-professional rounded-2xl font-semibold"
              >
                View Pricing
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="section-padding bg-gray-50/50">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-16">
            <h2 className="text-professional mb-4 text-gray-600">
              Trusted by leading companies worldwide
            </h2>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                TechCorp
              </span>
            </div>
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                InnovateLab
              </span>
            </div>
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                GrowthCo
              </span>
            </div>
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                ScaleUp
              </span>
            </div>
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                NextGen
              </span>
            </div>
            <div className="flex items-center justify-center h-16 bg-white rounded-xl shadow-professional p-4">
              <span className="text-lg font-semibold text-gray-700">
                FutureTech
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Everything you need to
              <span className="block gradient-text-animated">
                scale your business
              </span>
            </h2>
            <p className="text-professional-large max-w-3xl mx-auto">
              Our comprehensive SMS marketing platform provides all the tools
              and features you need to create, manage, and optimize successful
              campaigns.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-teal to-brand-orange rounded-2xl flex items-center justify-center shadow-professional">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Smart Messaging
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Create personalized SMS campaigns with intelligent automation,
                  dynamic content, and advanced targeting capabilities.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-orange to-brand-teal rounded-2xl flex items-center justify-center shadow-professional">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Audience Management
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Organize and segment your contacts with powerful filtering,
                  tagging, and list management tools for precise targeting.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-teal to-brand-orange rounded-2xl flex items-center justify-center shadow-professional">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Analytics & Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Track performance with detailed analytics, conversion metrics,
                  and actionable insights to optimize your campaigns.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-orange to-brand-teal rounded-2xl flex items-center justify-center shadow-professional">
                  <Send className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Bulk Messaging
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Send thousands of messages instantly with our high-performance
                  delivery infrastructure and global carrier network.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-teal to-brand-orange rounded-2xl flex items-center justify-center shadow-professional">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  Enterprise Security
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Bank-grade security with end-to-end encryption, compliance
                  certifications, and advanced data protection measures.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-professional-lg transition-all duration-300 border-0 shadow-professional bg-gradient-to-br from-white to-gray-50/50 hover:scale-105">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-brand-orange to-brand-teal rounded-2xl flex items-center justify-center shadow-professional">
                  <Clock className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  24/7 Support
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-professional">
                  Get expert help whenever you need it with our dedicated
                  support team and comprehensive knowledge base.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <div className="animate-fade-in animation-delay-1500">
        <Footer onNavigate={navigateTo} />
      </div>
    </div>
  );
};

export default Index;
