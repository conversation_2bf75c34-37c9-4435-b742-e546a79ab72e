import { useState } from "react";

const Index = () => {
  const [currentPage, setCurrentPage] = useState("home");

  // Debug: Add console log to check if component is rendering
  console.log("Index component rendering, currentPage:", currentPage);

  // Temporary simple test to debug blank page
  return (
    <div className="min-h-screen bg-white">
      <h1 className="text-4xl font-bold text-center pt-20 text-gray-900">
        Test - Website is Loading!
      </h1>
      <p className="text-center mt-4 text-gray-600">
        Current page: {currentPage}
      </p>
      <div className="text-center mt-8">
        <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-orange-500 rounded-full mx-auto animate-pulse"></div>
      </div>
    </div>
  );
};

export default Index;
