import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  MessageSquare,
  Users,
  BarChart3,
  Send,
  Shield,
  Clock,
  CheckCircle,
} from "lucide-react";
import { useState } from "react";
import Dashboard from "@/components/Dashboard";
import AuthPage from "@/components/AuthPage";
import PricingPage from "@/components/PricingPage";
import ContactPage from "@/components/ContactPage";
import SolutionsPage from "@/components/SolutionsPage";
import DeveloperDocsPage from "@/components/DeveloperDocsPage";
import AboutUsPage from "@/components/AboutUsPage";
import PrivacyPolicyPage from "@/components/PrivacyPolicyPage";
import TermsOfServicePage from "@/components/TermsOfServicePage";
import FAQPage from "@/components/FAQPage";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
const Index = () => {
  const [currentPage, setCurrentPage] = useState("home");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };
  const handleLogin = () => {
    setIsAuthenticated(true);
    setCurrentPage("dashboard");
  };
  const handleBack = () => {
    setCurrentPage("home");
  };
  const navigateTo = (page: string) => {
    setCurrentPage(page);
  };

  // Show appropriate page based on current state
  if (currentPage === "dashboard" && isAuthenticated) {
    return (
      <div className="page-fade-in">
        <Dashboard
          onBack={handleBack}
          onNavigate={navigateTo}
          onAuth={handleAuth}
        />
      </div>
    );
  }
  if (currentPage === "auth") {
    return (
      <div className="page-fade-in">
        <AuthPage
          onBack={handleBack}
          onLogin={handleLogin}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "pricing") {
    return (
      <div className="page-fade-in">
        <PricingPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "contact") {
    return (
      <div className="page-fade-in">
        <ContactPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "solutions") {
    return (
      <div className="page-fade-in">
        <SolutionsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "docs") {
    return (
      <div className="page-fade-in">
        <DeveloperDocsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "about") {
    return (
      <div className="page-fade-in">
        <AboutUsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "privacy") {
    return (
      <div className="page-fade-in">
        <PrivacyPolicyPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "terms") {
    return (
      <div className="page-fade-in">
        <TermsOfServicePage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "faq") {
    return (
      <div className="page-fade-in">
        <FAQPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      <div className="relative z-40 animate-fade-in">
        <Navigation
          onNavigate={navigateTo}
          onAuth={handleAuth}
          currentPage="home"
        />
      </div>

      {/* Hero Section with Colored Background Animation */}
      <section className="relative overflow-hidden z-30 bg-gradient-to-br from-[#4aa5af]/10 via-white to-[#368f98]/5 section-fade-in animation-delay-200">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 -z-10">
          {/* Large gradient orbs */}
          <div className="absolute top-10 left-0 w-96 h-96 bg-gradient-to-br from-[#368f98]/20 to-[#2d7a84]/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-20 right-0 w-[32rem] h-[32rem] bg-gradient-to-bl from-[#4aa5af]/15 to-[#368f98]/20 rounded-full blur-3xl animate-pulse animation-delay-1000"></div>
          <div className="absolute bottom-0 left-1/2 w-80 h-80 bg-gradient-to-tr from-[#2d7a84]/15 to-[#4aa5af]/20 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>

          {/* Medium gradient orbs */}
          <div className="absolute top-1/2 left-1/4 w-64 h-64 bg-gradient-to-br from-[#368f98]/10 to-[#2d7a84]/15 rounded-full blur-2xl animate-pulse animation-delay-500"></div>
          <div className="absolute bottom-1/4 right-1/4 w-72 h-72 bg-gradient-to-tl from-[#4aa5af]/10 to-[#368f98]/20 rounded-full blur-2xl animate-pulse animation-delay-1500"></div>

          {/* Floating animated dots */}
          <div className="absolute top-20 left-20 w-4 h-4 bg-[#368f98]/30 rounded-full animate-bounce animation-delay-1000"></div>
          <div className="absolute top-1/3 right-32 w-3 h-3 bg-[#4aa5af]/40 rounded-full animate-bounce animation-delay-2000"></div>
          <div className="absolute bottom-1/3 left-1/3 w-2 h-2 bg-[#2d7a84]/50 rounded-full animate-bounce animation-delay-500"></div>
          <div className="absolute top-32 right-1/3 w-5 h-5 bg-[#368f98]/25 rounded-full animate-bounce animation-delay-1500"></div>
          <div className="absolute bottom-32 left-32 w-3 h-3 bg-[#4aa5af]/35 rounded-full animate-bounce animation-delay-750"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center relative">
            {/* Marketing Banner Animation - Mobile First */}
            <div className="flex justify-center lg:justify-end order-1 lg:order-2 animate-slide-in-right animation-delay-500">
              <div className="relative">
                {/* Reduced size for mobile: w-64 h-64 instead of w-80 h-80 */}
                <div className="relative w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96">
                  {/* Phone Mockup with Floating Elements */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#368f98] to-[#2d7a84] rounded-3xl shadow-2xl transform rotate-12 animate-pulse">
                    <div className="absolute inset-4 bg-white rounded-2xl">
                      <div className="p-4 space-y-3">
                        <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                        <div className="h-3 bg-[#368f98] rounded w-1/2 animate-pulse"></div>
                      </div>
                    </div>
                  </div>

                  {/* Floating SMS Icons */}
                  <div className="absolute -top-4 -left-4 bg-[#368f98] text-white p-3 rounded-full shadow-lg animate-bounce">
                    <MessageSquare className="h-6 w-6" />
                  </div>
                  <div className="absolute -bottom-4 -right-4 bg-[#4aa5af] text-white p-3 rounded-full shadow-lg animate-bounce animation-delay-500">
                    <Send className="h-6 w-6" />
                  </div>
                  <div className="absolute top-1/2 -right-8 bg-[#2d7a84] text-white p-2 rounded-full shadow-lg animate-pulse animation-delay-1000">
                    <Users className="h-5 w-5" />
                  </div>

                  {/* Success Metrics Floating Cards */}
                  <div className="absolute -top-8 right-8 bg-white p-3 rounded-lg shadow-lg animate-fade-in animation-delay-2000">
                    <div className="text-xs text-gray-600">Delivery Rate</div>
                    <div className="text-lg font-bold text-[#368f98]">
                      99.9%
                    </div>
                  </div>
                  <div className="absolute bottom-0 -left-8 bg-white p-3 rounded-lg shadow-lg animate-fade-in animation-delay-1500">
                    <div className="text-xs text-gray-600">Messages Sent</div>
                    <div className="text-lg font-bold text-[#368f98]">1M+</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="text-center lg:text-left order-2 lg:order-1 animate-slide-in-left animation-delay-300">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
                Smart & Scalable Bulk SMS
                <span className="block text-[#368f98]">
                  Marketing Solutions
                </span>
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-3xl mx-auto lg:mx-0">
                Reach thousands of customers instantly with our professional SMS
                marketing platform. Create, send, and track SMS campaigns with
                ease.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button
                  size="lg"
                  onClick={handleAuth}
                  className="text-base sm:text-lg px-6 sm:px-8 py-2 sm:py-3 bg-[#368f98] hover:bg-[#2d7a84] transition-all duration-300 hover:scale-105"
                >
                  Get Started
                  <Send className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 section-fade-in animation-delay-500">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in-up animation-delay-600">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Everything You Need for SMS Marketing
          </h2>
          <p className="text-base sm:text-lg text-gray-600 px-4">
            Everything you need to create, send, and optimize SMS campaigns that
            drive results.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          <Card className="card-hover animate-fade-in-up animation-delay-1200">
            <CardHeader className="text-center sm:text-left">
              <Send className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
              <CardTitle className="text-lg sm:text-xl">
                High Delivery Rates
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Enterprise-grade infrastructure ensuring 99%+ delivery rates to
                global destinations.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover animate-fade-in-up animation-delay-800">
            <CardHeader className="text-center sm:text-left">
              <MessageSquare className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
              <CardTitle className="text-lg sm:text-xl">SMS Composer</CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Create engaging messages with templates, personalization, and
                real-time character counting.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover animate-fade-in-up animation-delay-900">
            <CardHeader className="text-center sm:text-left">
              <BarChart3 className="h-10 w-10 sm:h-12 sm:w-12 text-[#368f98] mb-4 mx-auto sm:mx-0" />
              <CardTitle className="text-lg sm:text-xl">
                Analytics & Reports
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Track delivery rates, engagement metrics, and campaign
                performance with detailed analytics.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 section-fade-in animation-delay-800">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in-up animation-delay-900">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-base sm:text-lg text-gray-600 px-4">
            Choose the perfect plan for your business needs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          {[
            {
              name: "STANDARD",
              price: "GHS12.00",
              period: "Non Expiry",
              description: "Perfect for small businesses starting with SMS",
              features: [
                "200 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: false,
            },
            {
              name: "PREMIUM",
              price: "GHS60.00",
              period: "Non Expiry",
              description: "Most popular for growing businesses",
              features: [
                "1000 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: true,
            },
            {
              name: "EXECUTIVE",
              price: "GHS600.00",
              period: "Non Expiry",
              description: "For large organizations with high volume needs",
              features: [
                "10,000 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: false,
            },
          ].map((plan, index) => (
            <Card
              key={index}
              className={`relative card-hover animate-fade-in-up animation-delay-${
                1000 + index * 100
              } ${plan.popular ? "border-[#368f98] border-2" : ""}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-[#368f98] text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold text-[#368f98]">
                  {plan.price}
                  <span className="text-base font-normal text-gray-600">
                    {plan.period}
                  </span>
                </div>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-[#368f98] mr-3 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  className={`w-full transition-all duration-300 hover:scale-105 ${
                    plan.popular
                      ? "bg-[#368f98] hover:bg-[#2d7a84]"
                      : "border-[#368f98] text-[#368f98] hover:bg-[#368f98] hover:text-white"
                  }`}
                  variant={plan.popular ? "default" : "outline"}
                  onClick={handleAuth}
                >
                  Get Started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section with grey background */}
      <section className="bg-gray-100 py-12 sm:py-20 section-fade-in animation-delay-1200">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 animate-fade-in-up animation-delay-1300">
          <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-gray-900">
            Ready to Start Your SMS Marketing?
          </h2>
          <p className="text-lg sm:text-xl text-gray-700 mb-6 sm:mb-8">
            Join thousands of businesses using Wispr SMS to reach their
            customers effectively.
          </p>
          <Button
            size="lg"
            onClick={handleAuth}
            className="text-base sm:text-lg px-6 sm:px-8 py-2 sm:py-3 bg-[#368f98] text-white hover:bg-[#2d7a84] transition-all duration-300 hover:scale-105"
          >
            Get Started Today
            <CheckCircle className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </div>
      </section>

      <div className="animate-fade-in animation-delay-1500">
        <Footer onNavigate={navigateTo} />
      </div>
    </div>
  );
};
export default Index;
