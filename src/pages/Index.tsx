import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  MessageSquare,
  Users,
  BarChart3,
  Send,
  Shield,
  Clock,
  CheckCircle,
} from "lucide-react";
import { useState } from "react";
import Dashboard from "@/components/Dashboard";
import AuthPage from "@/components/AuthPage";
import PricingPage from "@/components/PricingPage";
import ContactPage from "@/components/ContactPage";
import SolutionsPage from "@/components/SolutionsPage";
import DeveloperDocsPage from "@/components/DeveloperDocsPage";
import AboutUsPage from "@/components/AboutUsPage";
import PrivacyPolicyPage from "@/components/PrivacyPolicyPage";
import TermsOfServicePage from "@/components/TermsOfServicePage";
import FAQPage from "@/components/FAQPage";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
const Index = () => {
  const [currentPage, setCurrentPage] = useState("home");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const handleAuth = () => {
    window.open("https://app.wisprsms.com/", "_blank");
  };
  const handleLogin = () => {
    setIsAuthenticated(true);
    setCurrentPage("dashboard");
  };
  const handleBack = () => {
    setCurrentPage("home");
  };
  const navigateTo = (page: string) => {
    setCurrentPage(page);
  };

  // Show appropriate page based on current state
  if (currentPage === "dashboard" && isAuthenticated) {
    return (
      <div className="page-fade-in">
        <Dashboard
          onBack={handleBack}
          onNavigate={navigateTo}
          onAuth={handleAuth}
        />
      </div>
    );
  }
  if (currentPage === "auth") {
    return (
      <div className="page-fade-in">
        <AuthPage
          onBack={handleBack}
          onLogin={handleLogin}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "pricing") {
    return (
      <div className="page-fade-in">
        <PricingPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "contact") {
    return (
      <div className="page-fade-in">
        <ContactPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "solutions") {
    return (
      <div className="page-fade-in">
        <SolutionsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "docs") {
    return (
      <div className="page-fade-in">
        <DeveloperDocsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "about") {
    return (
      <div className="page-fade-in">
        <AboutUsPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "privacy") {
    return (
      <div className="page-fade-in">
        <PrivacyPolicyPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "terms") {
    return (
      <div className="page-fade-in">
        <TermsOfServicePage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  if (currentPage === "faq") {
    return (
      <div className="page-fade-in">
        <FAQPage
          onBack={handleBack}
          onAuth={handleAuth}
          onNavigate={navigateTo}
        />
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-white relative">
      <Navigation
        onNavigate={navigateTo}
        onAuth={handleAuth}
        currentPage="home"
      />

      {/* Hero Section with Modern Gradient Background */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary/5 via-white to-purple-500/5">
        {/* Modern Abstract Background Elements */}
        <div className="absolute inset-0 -z-10">
          {/* Large gradient orbs with modern colors */}
          <div className="absolute top-20 left-0 w-96 h-96 bg-gradient-to-br from-primary/20 to-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-40 right-0 w-[32rem] h-[32rem] bg-gradient-to-bl from-purple-500/15 to-primary/20 rounded-full blur-3xl animate-pulse animation-delay-1000"></div>
          <div className="absolute bottom-20 left-1/2 w-80 h-80 bg-gradient-to-tr from-blue-500/15 to-primary/20 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>

          {/* Geometric shapes for modern look */}
          <div className="absolute top-1/3 left-1/4 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-2xl rotate-45 animate-pulse animation-delay-500"></div>
          <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-gradient-to-tl from-purple-500/10 to-transparent rounded-full animate-pulse animation-delay-1500"></div>

          {/* Floating modern elements */}
          <div className="absolute top-32 left-32 w-6 h-6 bg-primary/30 rounded-lg rotate-45 animate-bounce animation-delay-1000"></div>
          <div className="absolute top-1/2 right-32 w-4 h-4 bg-purple-500/40 rounded-full animate-bounce animation-delay-2000"></div>
          <div className="absolute bottom-1/2 left-1/3 w-3 h-3 bg-blue-500/50 rounded-full animate-bounce animation-delay-500"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Modern Illustration - Mobile First */}
            <div className="flex justify-center lg:justify-end order-1 lg:order-2 animate-scale-in-modern animation-delay-500">
              <div className="relative">
                <div className="relative w-80 h-80 sm:w-96 sm:h-96 lg:w-[28rem] lg:h-[28rem]">
                  {/* Modern Phone Mockup */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary to-purple-600 rounded-3xl shadow-2xl transform rotate-6 animate-pulse">
                    <div className="absolute inset-4 bg-white rounded-2xl overflow-hidden">
                      <div className="p-6 space-y-4">
                        <div className="h-4 bg-gray-100 rounded-lg animate-pulse"></div>
                        <div className="h-4 bg-gray-100 rounded-lg w-3/4 animate-pulse"></div>
                        <div className="h-4 bg-primary/20 rounded-lg w-1/2 animate-pulse"></div>
                        <div className="space-y-2 mt-6">
                          <div className="h-3 bg-gray-50 rounded animate-pulse"></div>
                          <div className="h-3 bg-gray-50 rounded w-5/6 animate-pulse"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Modern Floating Icons */}
                  <div className="absolute -top-6 -left-6 bg-primary text-white p-4 rounded-2xl shadow-xl animate-bounce">
                    <MessageSquare className="h-7 w-7" />
                  </div>
                  <div className="absolute -bottom-6 -right-6 bg-purple-500 text-white p-4 rounded-2xl shadow-xl animate-bounce animation-delay-500">
                    <Send className="h-7 w-7" />
                  </div>
                  <div className="absolute top-1/2 -right-10 bg-blue-500 text-white p-3 rounded-xl shadow-lg animate-pulse animation-delay-1000">
                    <Users className="h-6 w-6" />
                  </div>

                  {/* Modern Metrics Cards */}
                  <div className="absolute -top-10 right-12 bg-white/90 backdrop-blur-sm p-4 rounded-xl shadow-xl border border-gray-100 animate-fade-in-up-modern animation-delay-2000">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Delivery Rate
                    </div>
                    <div className="text-2xl font-bold text-primary">99.9%</div>
                  </div>
                  <div className="absolute bottom-4 -left-12 bg-white/90 backdrop-blur-sm p-4 rounded-xl shadow-xl border border-gray-100 animate-fade-in-up-modern animation-delay-1500">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Messages Sent
                    </div>
                    <div className="text-2xl font-bold text-primary">1M+</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="text-center lg:text-left order-2 lg:order-1 animate-fade-in-up-modern animation-delay-300">
              <div className="mb-6">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-primary/10 text-primary border border-primary/20">
                  ✨ Trusted by 10,000+ businesses worldwide
                </span>
              </div>
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-[1.1]">
                Smart & Scalable
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-purple-600">
                  SMS Marketing
                </span>
                <span className="block text-gray-900">Solutions</span>
              </h1>
              <p className="text-xl sm:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Reach thousands of customers instantly with our professional SMS
                marketing platform. Create, send, and track campaigns with ease.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button
                  size="lg"
                  onClick={handleAuth}
                  className="text-lg px-8 py-4 btn-modern bg-primary hover:bg-primary/90 text-white border-0 shadow-lg"
                >
                  Get Started Free
                  <Send className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  onClick={() => navigateTo("pricing")}
                  className="text-lg px-8 py-4 btn-modern border-2 border-gray-300 text-gray-700 hover:border-primary hover:text-primary bg-white"
                >
                  View Pricing
                </Button>
              </div>
              <div className="mt-8 flex items-center justify-center lg:justify-start space-x-6 text-sm text-gray-500">
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  No setup fees
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  99.9% uptime
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  24/7 support
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-16 bg-gray-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 animate-fade-in-up-modern animation-delay-300">
            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-8">
              Trusted by leading companies worldwide
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
              {/* Placeholder for company logos - you can replace with actual logos */}
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse">
                <span className="text-xs font-medium text-gray-400">
                  Company 1
                </span>
              </div>
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse animation-delay-100">
                <span className="text-xs font-medium text-gray-400">
                  Company 2
                </span>
              </div>
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse animation-delay-200">
                <span className="text-xs font-medium text-gray-400">
                  Company 3
                </span>
              </div>
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse animation-delay-300">
                <span className="text-xs font-medium text-gray-400">
                  Company 4
                </span>
              </div>
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse animation-delay-500 md:col-span-2 lg:col-span-1">
                <span className="text-xs font-medium text-gray-400">
                  Company 5
                </span>
              </div>
              <div className="flex items-center justify-center h-12 bg-gray-200 rounded-lg animate-pulse animation-delay-750 md:col-span-2 lg:col-span-1">
                <span className="text-xs font-medium text-gray-400">
                  Company 6
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16 animate-fade-in-up-modern animation-delay-300">
          <div className="mb-4">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-primary/10 text-primary">
              🚀 Powerful Features
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Everything You Need for
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-purple-600">
              SMS Marketing Success
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Powerful tools and enterprise-grade infrastructure to create, send,
            and optimize SMS campaigns that drive real results for your
            business.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-500">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <Send className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                High Delivery Rates
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Enterprise-grade infrastructure ensuring 99.9%+ delivery rates
                to global destinations with real-time monitoring.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-700">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <MessageSquare className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                Smart Composer
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Create engaging messages with AI-powered templates,
                personalization, and real-time character counting.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-900">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                Advanced Analytics
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Track delivery rates, engagement metrics, and campaign
                performance with detailed real-time analytics.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-1100">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                Enterprise Security
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Bank-level security with end-to-end encryption, compliance
                certifications, and data protection.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-1300">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                Smart Scheduling
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Schedule campaigns for optimal delivery times with timezone
                intelligence and automated sending.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-1500">
            <CardHeader className="text-center p-0">
              <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mb-6 mx-auto">
                <Users className="h-8 w-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold mb-4">
                Contact Management
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Organize contacts with smart segmentation, import/export tools,
                and automated list management.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up-modern animation-delay-300">
            <div className="mb-4">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-primary/10 text-primary">
                💬 Customer Stories
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
              Loved by
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-purple-600">
                Thousands of Businesses
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how companies around the world are using Wispr SMS to grow
              their business and connect with customers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-500">
              <CardContent className="p-0">
                <div className="flex items-center mb-6">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className="w-5 h-5 fill-current"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <blockquote className="text-lg text-gray-700 mb-6 leading-relaxed">
                  "Wispr SMS has transformed our customer communication. The
                  delivery rates are incredible and the analytics help us
                  optimize every campaign."
                </blockquote>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    S
                  </div>
                  <div className="ml-4">
                    <div className="font-semibold text-gray-900">
                      Sarah Johnson
                    </div>
                    <div className="text-gray-500">
                      Marketing Director, TechCorp
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-700">
              <CardContent className="p-0">
                <div className="flex items-center mb-6">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className="w-5 h-5 fill-current"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <blockquote className="text-lg text-gray-700 mb-6 leading-relaxed">
                  "The platform is incredibly user-friendly and the customer
                  support is outstanding. We've seen a 40% increase in customer
                  engagement."
                </blockquote>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    M
                  </div>
                  <div className="ml-4">
                    <div className="font-semibold text-gray-900">
                      Michael Chen
                    </div>
                    <div className="text-gray-500">CEO, RetailPlus</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover p-8 border-0 shadow-lg bg-white animate-fade-in-up-modern animation-delay-900">
              <CardContent className="p-0">
                <div className="flex items-center mb-6">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className="w-5 h-5 fill-current"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <blockquote className="text-lg text-gray-700 mb-6 leading-relaxed">
                  "Switching to Wispr SMS was the best decision we made. The ROI
                  on our campaigns has increased by 300% in just 6 months."
                </blockquote>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    E
                  </div>
                  <div className="ml-4">
                    <div className="font-semibold text-gray-900">
                      Emily Rodriguez
                    </div>
                    <div className="text-gray-500">
                      Growth Manager, StartupXYZ
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in-up animation-delay-900">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-base sm:text-lg text-gray-600 px-4">
            Choose the perfect plan for your business needs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
          {[
            {
              name: "STANDARD",
              price: "GHS12.00",
              period: "Non Expiry",
              description: "Perfect for small businesses starting with SMS",
              features: [
                "200 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: false,
            },
            {
              name: "PREMIUM",
              price: "GHS60.00",
              period: "Non Expiry",
              description: "Most popular for growing businesses",
              features: [
                "1000 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: true,
            },
            {
              name: "EXECUTIVE",
              price: "GHS600.00",
              period: "Non Expiry",
              description: "For large organizations with high volume needs",
              features: [
                "10,000 Total SMS",
                "0.06GHS / SMS",
                "Import/Export Contact List",
                "Sender ID Verification",
                "API Access",
                "24/7 Customer Service",
              ],
              popular: false,
            },
          ].map((plan, index) => (
            <Card
              key={index}
              className={`relative card-hover animate-fade-in-up animation-delay-${
                1000 + index * 100
              } ${plan.popular ? "border-[#368f98] border-2" : ""}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-[#368f98] text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold text-[#368f98]">
                  {plan.price}
                  <span className="text-base font-normal text-gray-600">
                    {plan.period}
                  </span>
                </div>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-[#368f98] mr-3 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  className={`w-full transition-all duration-300 hover:scale-105 ${
                    plan.popular
                      ? "bg-[#368f98] hover:bg-[#2d7a84]"
                      : "border-[#368f98] text-[#368f98] hover:bg-[#368f98] hover:text-white"
                  }`}
                  variant={plan.popular ? "default" : "outline"}
                  onClick={handleAuth}
                >
                  Get Started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section with grey background */}
      <section className="bg-gray-100 py-12 sm:py-20 section-fade-in animation-delay-1200">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 animate-fade-in-up animation-delay-1300">
          <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-gray-900">
            Ready to Start Your SMS Marketing?
          </h2>
          <p className="text-lg sm:text-xl text-gray-700 mb-6 sm:mb-8">
            Join thousands of businesses using Wispr SMS to reach their
            customers effectively.
          </p>
          <Button
            size="lg"
            onClick={handleAuth}
            className="text-base sm:text-lg px-6 sm:px-8 py-2 sm:py-3 bg-[#368f98] text-white hover:bg-[#2d7a84] transition-all duration-300 hover:scale-105"
          >
            Get Started Today
            <CheckCircle className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </div>
      </section>

      <div className="animate-fade-in animation-delay-1500">
        <Footer onNavigate={navigateTo} />
      </div>
    </div>
  );
};
export default Index;
